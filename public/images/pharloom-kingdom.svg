<svg width="800" height="500" viewBox="0 0 800 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="forestGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#065F46;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#064E3B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="treeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="url(#forestGradient)"/>
  
  <!-- Large trees in background -->
  <ellipse cx="150" cy="400" rx="40" ry="120" fill="url(#treeGradient)" opacity="0.7"/>
  <ellipse cx="650" cy="380" rx="50" ry="140" fill="url(#treeGradient)" opacity="0.6"/>
  <ellipse cx="400" cy="420" rx="35" ry="100" fill="url(#treeGradient)" opacity="0.8"/>
  
  <!-- Tree canopies -->
  <circle cx="150" cy="280" r="60" fill="#10B981" opacity="0.8"/>
  <circle cx="650" cy="240" r="70" fill="#10B981" opacity="0.7"/>
  <circle cx="400" cy="320" r="50" fill="#10B981" opacity="0.9"/>
  
  <!-- Smaller vegetation -->
  <circle cx="80" cy="450" r="25" fill="#059669" opacity="0.6"/>
  <circle cx="300" cy="470" r="20" fill="#059669" opacity="0.7"/>
  <circle cx="500" cy="460" r="30" fill="#059669" opacity="0.5"/>
  <circle cx="720" cy="480" r="22" fill="#059669" opacity="0.8"/>
  
  <!-- Ancient structures -->
  <rect x="200" y="350" width="80" height="120" fill="#374151" opacity="0.8"/>
  <polygon points="200,350 240,320 280,350" fill="#4B5563" opacity="0.8"/>
  
  <rect x="520" y="380" width="60" height="90" fill="#374151" opacity="0.7"/>
  <polygon points="520,380 550,360 580,380" fill="#4B5563" opacity="0.7"/>
  
  <!-- Mysterious glowing elements -->
  <circle cx="240" cy="380" r="15" fill="url(#glowGradient)">
    <animate attributeName="r" values="10;20;10" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="550" cy="410" r="12" fill="url(#glowGradient)">
    <animate attributeName="r" values="8;18;8" dur="5s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Silk webs -->
  <path d="M100 200 L200 250 L150 300 L50 250 Z" stroke="#E5E7EB" stroke-width="1" fill="none" opacity="0.4"/>
  <path d="M600 180 L700 220 L650 270 L550 230 Z" stroke="#E5E7EB" stroke-width="1" fill="none" opacity="0.3"/>
  
  <!-- Flying insects/creatures -->
  <circle cx="350" cy="200" r="3" fill="#8B5CF6" opacity="0.6">
    <animateTransform attributeName="transform" type="translate" values="0,0; 50,-20; 0,0" dur="6s" repeatCount="indefinite"/>
  </circle>
  <circle cx="450" cy="180" r="2" fill="#3B82F6" opacity="0.5">
    <animateTransform attributeName="transform" type="translate" values="0,0; -30,30; 0,0" dur="8s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Foreground grass -->
  <path d="M0 480 Q100 470 200 480 T400 475 T600 480 T800 475 L800 500 L0 500 Z" fill="#047857" opacity="0.9"/>
  
  <!-- Atmospheric particles -->
  <circle cx="120" cy="150" r="1" fill="#F8FAFC" opacity="0.3">
    <animate attributeName="cy" values="150;450;150" dur="10s" repeatCount="indefinite"/>
  </circle>
  <circle cx="680" cy="120" r="1.5" fill="#F8FAFC" opacity="0.4">
    <animate attributeName="cy" values="120;480;120" dur="12s" repeatCount="indefinite"/>
  </circle>
</svg>
