<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#1E293B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F172A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="hornetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#skyGradient)"/>
  
  <!-- Platforms -->
  <rect x="50" y="320" width="120" height="20" fill="#374151" rx="10"/>
  <rect x="250" y="280" width="100" height="15" fill="#374151" rx="7"/>
  <rect x="450" y="240" width="80" height="12" fill="#374151" rx="6"/>
  
  <!-- Hornet (main character) -->
  <g transform="translate(300, 200)">
    <!-- Body -->
    <ellipse cx="0" cy="20" rx="15" ry="25" fill="#1F2937"/>
    <!-- Head -->
    <circle cx="0" cy="0" r="12" fill="#1F2937"/>
    <!-- Horns -->
    <path d="M-8 -8 L-12 -20 M8 -8 L12 -20" stroke="url(#hornetGradient)" stroke-width="3" stroke-linecap="round"/>
    <!-- Eyes -->
    <circle cx="-4" cy="-2" r="2" fill="#3B82F6"/>
    <circle cx="4" cy="-2" r="2" fill="#3B82F6"/>
    <!-- Needle -->
    <path d="M-30 10 L30 0" stroke="#8B5CF6" stroke-width="3" stroke-linecap="round"/>
    <circle cx="30" cy="0" r="2" fill="#8B5CF6"/>
    <!-- Silk trail -->
    <path d="M0 45 Q-20 60 -40 50 Q-60 40 -80 55" stroke="#E5E7EB" stroke-width="2" fill="none" stroke-dasharray="4,3"/>
  </g>
  
  <!-- Action effects -->
  <circle cx="330" cy="200" r="30" fill="url(#glowGradient)" opacity="0.6">
    <animate attributeName="r" values="20;40;20" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Enemy/Boss silhouette -->
  <g transform="translate(500, 150)">
    <ellipse cx="0" cy="30" rx="25" ry="40" fill="#0F172A" opacity="0.8"/>
    <circle cx="0" cy="0" r="20" fill="#0F172A" opacity="0.8"/>
    <path d="M-15 -15 L-25 -30 M15 -15 L25 -30" stroke="#DC2626" stroke-width="2" stroke-linecap="round"/>
    <circle cx="-6" cy="-5" r="2" fill="#DC2626"/>
    <circle cx="6" cy="-5" r="2" fill="#DC2626"/>
  </g>
  
  <!-- Atmospheric particles -->
  <circle cx="100" cy="100" r="1" fill="#F8FAFC" opacity="0.6">
    <animate attributeName="cy" values="100;350;100" dur="8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="400" cy="80" r="1.5" fill="#F8FAFC" opacity="0.4">
    <animate attributeName="cy" values="80;380;80" dur="10s" repeatCount="indefinite"/>
  </circle>
  <circle cx="550" cy="120" r="1" fill="#F8FAFC" opacity="0.7">
    <animate attributeName="cy" values="120;370;120" dur="12s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Silk web in background -->
  <path d="M50 50 L150 100 L100 150 L0 100 Z" stroke="#E5E7EB" stroke-width="1" fill="none" opacity="0.3"/>
  <path d="M450 60 L550 90 L520 140 L420 110 Z" stroke="#E5E7EB" stroke-width="1" fill="none" opacity="0.2"/>
</svg>
