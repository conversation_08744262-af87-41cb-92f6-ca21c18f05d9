<!-- This would be a PNG file, but for demonstration, I'll create an SVG version -->
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="hornGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1F2937;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#1E293B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0F172A;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="180" height="180" rx="40" fill="url(#bgGradient)"/>
  
  <!-- Hornet's body -->
  <ellipse cx="90" cy="105" rx="35" ry="45" fill="url(#bodyGradient)"/>
  
  <!-- Hornet's head -->
  <circle cx="90" cy="70" r="25" fill="url(#bodyGradient)"/>
  
  <!-- Hornet's horns -->
  <path d="M65 55 L50 25 M115 55 L130 25" stroke="url(#hornGradient)" stroke-width="8" stroke-linecap="round"/>
  
  <!-- Eyes -->
  <circle cx="80" cy="65" r="6" fill="#3B82F6"/>
  <circle cx="100" cy="65" r="6" fill="#3B82F6"/>
  
  <!-- Needle/weapon -->
  <path d="M30 120 L150 95" stroke="#8B5CF6" stroke-width="8" stroke-linecap="round"/>
  <circle cx="150" cy="95" r="6" fill="#8B5CF6"/>
  
  <!-- Silk threads -->
  <path d="M90 150 Q120 130 150 120" stroke="#E5E7EB" stroke-width="4" fill="none" stroke-dasharray="8,6"/>
  <path d="M70 140 Q40 125 20 110" stroke="#E5E7EB" stroke-width="3" fill="none" stroke-dasharray="6,4"/>
</svg>
