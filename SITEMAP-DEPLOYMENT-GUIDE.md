# Sitemap Deployment Troubleshooting Guide

## ✅ Current Status

Your sitemap configuration is **CORRECT** and working properly:

- ✅ `src/app/sitemap.ts` exists and has proper structure
- ✅ Uses correct Next.js 15 App Router API
- ✅ Builds successfully (visible in build output)
- ✅ Generates valid XML content
- ✅ Contains correct domain (www.silksong.org)
- ✅ Includes all expected routes (/about, /blog)
- ✅ No conflicting static files

## 🔍 Why You Might See 404 in Production

The sitemap works locally but returns 404 in production. Here are the most common causes:

### 1. **Hosting Platform Compatibility**
Some hosting platforms don't fully support Next.js 15 App Router features:

**✅ Fully Compatible:**
- Vercel (recommended)
- Netlify (with Next.js runtime)
- Railway
- Render

**⚠️ May Need Configuration:**
- AWS Amplify
- Azure Static Web Apps
- GitHub Pages (limited support)

**❌ Not Compatible:**
- Basic static hosting
- Older hosting platforms

### 2. **Build Deployment Issues**
- Ensure the entire `.next` folder is deployed
- Verify the hosting platform runs `npm run build`
- Check that server-side routes are supported

### 3. **Server Configuration**
Some servers need explicit configuration for dynamic routes:

```nginx
# Nginx example
location /sitemap.xml {
    proxy_pass http://localhost:3000/sitemap.xml;
}
```

## 🚀 Deployment Checklist

### Before Deployment:
- [ ] Run `npm run build` locally to verify it builds
- [ ] Check that sitemap.xml appears in build output
- [ ] Verify no static sitemap.xml exists in public/
- [ ] Confirm domain in sitemap.ts matches production domain

### After Deployment:
- [ ] Test `https://www.silksong.org/sitemap.xml` directly
- [ ] Check browser developer tools for any errors
- [ ] Verify hosting platform supports Next.js App Router
- [ ] Check server logs for sitemap generation errors

## 🛠️ Quick Fixes

### Fix 1: Add Static Fallback (Temporary)
If you need immediate sitemap access, create a static version:

```bash
# Generate static sitemap from build output
cp .next/server/app/sitemap.xml.body public/sitemap.xml
```

### Fix 2: Verify Hosting Platform
Check your hosting platform's Next.js support:
- Vercel: Automatic support
- Netlify: Ensure using `@netlify/plugin-nextjs`
- Others: Check documentation for App Router support

### Fix 3: Check Environment Variables
Ensure your hosting platform has correct environment variables:
```bash
NODE_ENV=production
```

## 🔧 Advanced Troubleshooting

### Test Sitemap Generation Locally:
```bash
npm run build
npm run start
curl http://localhost:3000/sitemap.xml
```

### Check Build Output:
```bash
# Verify sitemap files exist after build
ls -la .next/server/app/sitemap.xml*
```

### Debug in Production:
1. Check browser Network tab when accessing sitemap.xml
2. Look for 404, 500, or other error codes
3. Check if the request reaches your server
4. Verify the hosting platform's logs

## 📞 Platform-Specific Solutions

### Vercel:
- Should work automatically
- Check deployment logs for errors
- Verify using Next.js 15 in package.json

### Netlify:
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = ".next"

[[plugins]]
  package = "@netlify/plugin-nextjs"
```

### Other Platforms:
- Ensure they support Next.js server-side rendering
- Check if they require specific configuration for dynamic routes
- Consider using static export if dynamic features aren't supported

## 🎯 Expected Sitemap Content

Your sitemap should contain:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
<url>
<loc>https://www.silksong.org</loc>
<lastmod>2025-06-30T06:36:56.249Z</lastmod>
<changefreq>daily</changefreq>
<priority>1</priority>
</url>
<url>
<loc>https://www.silksong.org/about</loc>
<lastmod>2025-06-30T06:36:56.249Z</lastmod>
<changefreq>monthly</changefreq>
<priority>0.8</priority>
</url>
<url>
<loc>https://www.silksong.org/blog</loc>
<lastmod>2025-06-30T06:36:56.249Z</lastmod>
<changefreq>daily</changefreq>
<priority>0.9</priority>
</url>
</urlset>
```

## 📝 Next Steps

1. **Immediate**: Check your hosting platform's Next.js App Router support
2. **Short-term**: Test deployment on Vercel (known to work)
3. **Long-term**: Monitor sitemap accessibility and submit to search engines

Your sitemap configuration is correct - the issue is likely hosting platform compatibility or deployment configuration.
