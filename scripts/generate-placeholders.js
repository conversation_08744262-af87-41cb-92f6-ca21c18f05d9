const fs = require('fs');
const path = require('path');

const imagesDir = path.join(__dirname, '../public/images');

// Ensure images directory exists
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Generate different placeholder images
const placeholders = [
  {
    name: 'silksong-background.jpg',
    width: 1920,
    height: 1080,
    title: 'Silksong Background',
    colors: ['#1e293b', '#0f172a', '#3b82f6']
  },
  {
    name: 'silksong-intro.jpg',
    width: 800,
    height: 450,
    title: 'Gameplay',
    colors: ['#2d1b69', '#1e1b4b', '#6366f1']
  },
  {
    name: 'feature-combat.jpg',
    width: 600,
    height: 400,
    title: 'Combat',
    colors: ['#7c2d12', '#991b1b', '#ef4444']
  },
  {
    name: 'feature-enemies.jpg',
    width: 600,
    height: 400,
    title: 'Enemies',
    colors: ['#14532d', '#166534', '#22c55e']
  },
  {
    name: 'feature-music.jpg',
    width: 600,
    height: 400,
    title: 'Music',
    colors: ['#581c87', '#7c3aed', '#a855f7']
  }
];

placeholders.forEach(placeholder => {
  const svg = `<svg width="${placeholder.width}" height="${placeholder.height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${placeholder.colors[0]};stop-opacity:1" />
      <stop offset="50%" style="stop-color:${placeholder.colors[1]};stop-opacity:1" />
      <stop offset="100%" style="stop-color:${placeholder.colors[2]};stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#bg)"/>
  <circle cx="${placeholder.width/2}" cy="${placeholder.height/2}" r="${Math.min(placeholder.width, placeholder.height)/8}" fill="white" opacity="0.1"/>
  <circle cx="${placeholder.width/2}" cy="${placeholder.height/2}" r="${Math.min(placeholder.width, placeholder.height)/16}" fill="white" opacity="0.3"/>
  <text x="${placeholder.width/2}" y="${placeholder.height/2 + 8}" font-family="Arial, sans-serif" font-size="${Math.min(placeholder.width, placeholder.height)/20}" fill="white" text-anchor="middle" opacity="0.8">${placeholder.title}</text>
</svg>`;

  // For now, we'll create SVG files instead of JPG
  const svgPath = path.join(imagesDir, placeholder.name.replace('.jpg', '.svg'));
  fs.writeFileSync(svgPath, svg);
  console.log(`Generated ${svgPath}`);
});

console.log('All placeholder images generated!');
