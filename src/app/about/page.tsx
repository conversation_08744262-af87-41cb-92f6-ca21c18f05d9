import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "About - Silksong Info",
  description: "Learn about Silksong Info, your trusted source for Hollow Knight: Silksong news and updates.",
}

export default function About() {
  return (
    <div className="min-h-screen py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              About Silksong Info
            </h1>
            <p className="text-xl text-gray-600">
              Your trusted source for Hollow Knight: Silksong news and updates
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <Card>
              <CardHeader>
                <CardTitle>Our Mission</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  We are dedicated to providing the most up-to-date and accurate information 
                  about Hollow Knight: Silksong. Our team of passionate fans works tirelessly 
                  to bring you the latest news, updates, and insights about this highly 
                  anticipated game.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>What We Do</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 leading-relaxed">
                  From official announcements to community discussions, we cover everything 
                  related to Silksong. We also provide helpful guides, system requirements, 
                  and recommendations for other great Metroidvania games to enjoy while 
                  you wait.
                </p>
              </CardContent>
            </Card>
          </div>

          <Card className="mb-12">
            <CardHeader>
              <CardTitle>About Team Cherry</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600 leading-relaxed">
                Team Cherry is an independent game development studio based in Adelaide, Australia. 
                The team consists of Ari Gibson (art and animation), William Pellen (programming), 
                and Christopher Larkin (music and sound design).
              </p>
              <p className="text-gray-600 leading-relaxed">
                They gained worldwide recognition with their debut game Hollow Knight, which was 
                initially funded through Kickstarter and became one of the most beloved indie games 
                of all time. The success of Hollow Knight led to the development of its sequel, 
                Hollow Knight: Silksong.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Disclaimer</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 leading-relaxed">
                This website is a fan-made project and is not officially affiliated with Team Cherry 
                or Hollow Knight: Silksong. All game content, images, and trademarks belong to their 
                respective owners. We strive to provide accurate information, but always refer to 
                official sources for the most up-to-date announcements.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
