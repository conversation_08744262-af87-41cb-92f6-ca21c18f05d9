import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Calendar, Clock } from "lucide-react"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Blog - Silksong Info",
  description: "Latest news, analysis, and insights about <PERSON> Knight: Silksong from our team of passionate fans.",
}

const blogPosts = [
  {
    title: "Silksong Confirmed for 2025: Everything We Know",
    excerpt: "Team Cherry finally breaks their silence with an official 2025 release window announcement during Nintendo Direct.",
    date: "April 2, 2025",
    readTime: "5 min read",
    category: "News"
  },
  {
    title: "Analyzing the Latest Silksong Gameplay Footage",
    excerpt: "A detailed breakdown of the 3-second clip shown during the Nintendo Direct, revealing new mechanics and environments.",
    date: "April 3, 2025", 
    readTime: "8 min read",
    category: "Analysis"
  },
  {
    title: "The Evolution of Hornet: From NPC to Protagonist",
    excerpt: "How <PERSON><PERSON>'s character has evolved from her role in the original Hollow Knight to becoming the star of Silksong.",
    date: "March 28, 2025",
    readTime: "6 min read",
    category: "Character Study"
  },
  {
    title: "Best Metroidvania Games to Play While Waiting for Silksong",
    excerpt: "A curated list of exceptional Metroidvania games that will help you pass the time until Silksong's release.",
    date: "March 25, 2025",
    readTime: "10 min read",
    category: "Recommendations"
  },
  {
    title: "Silksong's Music: What to Expect from Christopher Larkin",
    excerpt: "Exploring the musical direction of Silksong based on previews and <PERSON> Larkin's previous work.",
    date: "March 20, 2025",
    readTime: "7 min read",
    category: "Music"
  },
  {
    title: "The Kingdom of Pharloom: A New World to Explore",
    excerpt: "Everything we know about Silksong's setting and how it differs from Hallownest in the original game.",
    date: "March 15, 2025",
    readTime: "9 min read",
    category: "World Building"
  }
]

export default function Blog() {
  return (
    <div className="min-h-screen py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Silksong Blog
            </h1>
            <p className="text-xl text-gray-600">
              Latest news, analysis, and insights about Hollow Knight: Silksong
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                      {post.category}
                    </span>
                  </div>
                  <CardTitle className="text-xl leading-tight hover:text-blue-600 transition-colors">
                    {post.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 leading-relaxed mb-4">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{post.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600">
              More articles coming soon! Stay tuned for the latest Silksong updates.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
