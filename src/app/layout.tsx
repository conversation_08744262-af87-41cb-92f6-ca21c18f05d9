import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";

export const metadata: Metadata = {
  title: "Hollow Knight: Silksong - News and Updates",
  description: "Your ultimate source for Hollow Knight: Silksong news, updates, and community discussions. Stay informed about the most anticipated Metroidvania sequel.",
  keywords: "Hollow Knight, Silksong, Team Cherry, Metroidvania, indie games, gaming news, 2025 release",
  authors: [{ name: "Silksong Info Team" }],
  creator: "Silksong Info Team",
  publisher: "Silksong Info",
  metadataBase: new URL('https://www.silksong.org'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Hollow Knight: Silksong - News and Updates",
    description: "Your ultimate source for Hollow Knight: Silksong news, updates, and community discussions.",
    type: "website",
    locale: "en_US",
    siteName: "Silksong Info",
    url: 'https://www.silksong.org',
    images: [
      {
        url: '/images/silksong-background.webp',
        width: 1920,
        height: 1080,
        alt: 'Hollow Knight: Silksong',
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Hollow Knight: Silksong - News and Updates",
    description: "Your ultimate source for Hollow Knight: Silksong news, updates, and community discussions.",
    images: ['/images/silksong-background.webp'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    // Add your verification codes here when you have them
    // google: 'your-google-verification-code',
    // bing: 'your-bing-verification-code',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Silksong Info',
    description: 'Your ultimate source for Hollow Knight: Silksong news, updates, and community discussions.',
    url: 'https://www.silksong.org',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://www.silksong.org/search?q={search_term_string}',
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: 'Silksong Info Team',
      url: 'https://www.silksong.org',
    },
    about: {
      '@type': 'VideoGame',
      name: 'Hollow Knight: Silksong',
      developer: {
        '@type': 'Organization',
        name: 'Team Cherry',
      },
      genre: 'Metroidvania',
      platform: ['PC', 'Nintendo Switch', 'PlayStation', 'Xbox'],
    },
  };

  return (
    <html lang="en">
      <head>
        {/* Google AdSense */}
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" crossOrigin="anonymous"></script>
        <meta name="google-adsense-account" content="ca-pub-****************" />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-0V2EM9CL4H"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-0V2EM9CL4H');
            `,
          }}
        />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3B82F6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Silksong Info" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body className="antialiased">
        <div className="min-h-screen flex flex-col">
          <Navbar />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
