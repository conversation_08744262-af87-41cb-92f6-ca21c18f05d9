import Link from "next/link"

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-slate-900 border-t border-slate-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">H</span>
              </div>
              <div>
                <div className="font-bold text-white text-lg">Hornet&apos;s Journey</div>
                <div className="text-slate-400 text-sm">Silksong Hub</div>
              </div>
            </div>
            <p className="text-slate-400 text-sm leading-relaxed">
              Your ultimate destination for Hollow Knight: Silksong news, updates, and community discussions.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-white font-semibold">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">Home</Link>
              <Link href="/blog" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">News</Link>
              <Link href="/about" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">About</Link>
              <a href="https://store.steampowered.com/app/1030300/Hollow_Knight_Silksong/" target="_blank" rel="noopener noreferrer" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">Steam Page</a>
            </div>
          </div>

          {/* Community */}
          <div className="space-y-4">
            <h3 className="text-white font-semibold">Community</h3>
            <div className="space-y-2">
              <a href="https://www.reddit.com/r/Silksong/" target="_blank" rel="noopener noreferrer" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">Reddit</a>
              <a href="https://discord.gg/hollowknight" target="_blank" rel="noopener noreferrer" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">Discord</a>
              <a href="https://twitter.com/TeamCherryGames" target="_blank" rel="noopener noreferrer" className="block text-slate-400 hover:text-purple-300 text-sm transition-colors">Twitter</a>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-700 pt-8">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              © {currentYear} Hornet&apos;s Journey. Fan-made site. Not affiliated with Team Cherry.
            </p>
            <div className="flex space-x-6 mt-4 sm:mt-0">
              <button className="text-slate-400 hover:text-purple-300 text-sm transition-colors">
                English
              </button>
              <button className="text-slate-400 hover:text-purple-300 text-sm transition-colors">
                Español
              </button>
              <button className="text-slate-400 hover:text-purple-300 text-sm transition-colors">
                Português
              </button>
              <button className="text-slate-400 hover:text-purple-300 text-sm transition-colors">
                Français
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
