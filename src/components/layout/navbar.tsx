"use client"

import Link from "next/link"
import { useState } from "react"
import { Menu, X } from "lucide-react"

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen)

  return (
    <nav className="bg-slate-900 sticky top-0 z-50 w-full border-b border-slate-700 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-18">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center shadow-md">
                <span className="text-white font-bold text-xl">H</span>
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-white text-lg">Hornet&apos;s Journey</span>
                <span className="text-slate-300 text-xs">Silksong Hub</span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="text-white hover:text-purple-300 text-sm font-medium transition-colors duration-200"
            >
              Home
            </Link>
            <Link
              href="/blog"
              className="text-slate-300 hover:text-white text-sm font-medium transition-colors duration-200"
            >
              News
            </Link>
            <Link
              href="/about"
              className="text-slate-300 hover:text-white text-sm font-medium transition-colors duration-200"
            >
              About
            </Link>
            <div className="bg-gradient-to-r from-purple-500 to-blue-600 px-4 py-2 rounded-lg">
              <Link
                href="https://store.steampowered.com/app/1030300/Hollow_Knight_Silksong/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white text-sm font-medium hover:text-purple-100 transition-colors duration-200"
              >
                Wishlist
              </Link>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <button
              onClick={toggleMenu}
              className="p-2 text-white hover:text-purple-300 transition-colors duration-200"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-slate-700 bg-slate-800">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <Link
                href="/"
                className="text-white hover:text-purple-300 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                href="/blog"
                className="text-slate-300 hover:text-white block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                News
              </Link>
              <Link
                href="/about"
                className="text-slate-300 hover:text-white block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                About
              </Link>
              <Link
                href="https://store.steampowered.com/app/1030300/Hollow_Knight_Silksong/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-purple-300 hover:text-purple-100 block px-3 py-2 text-base font-medium transition-colors duration-200"
                onClick={() => setIsMenuOpen(false)}
              >
                Wishlist on Steam
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
