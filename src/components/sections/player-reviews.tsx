const reviews = [
  {
    id: 1,
    name: "<PERSON>",
    text: "I played the demo at PAX and was completely blown away. <PERSON><PERSON>'s movement feels so smooth and responsive. This is definitely going to surpass the original!"
  },
  {
    id: 2,
    name: "<PERSON>",
    text: "The art style from what we've seen so far is absolutely gorgeous. Team <PERSON> has outdone themselves with the environments and character designs. Can't wait for the full release!"
  },
  {
    id: 3,
    name: "<PERSON><PERSON><PERSON>",
    text: "The music samples released so far are just incredible. <PERSON> is a genius. I've been listening to the teasers on repeat and can't wait to hear the full soundtrack!"
  },
  {
    id: 4,
    name: "<PERSON>",
    text: "The combat in Silksong looks so much more fast-paced and dynamic compared to <PERSON>. <PERSON><PERSON>'s silk abilities create a whole new dimension to the gameplay. Day one purchase for me!"
  },
  {
    id: 5,
    name: "<PERSON>",
    text: "I've watched every trailer at least 20 times. The boss designs we've seen so far are absolutely terrifying and beautiful at the same time. My only concern is that 2025 is still too far away!"
  },
  {
    id: 6,
    name: "<PERSON>",
    text: "The fact that <PERSON> Cherry is taking their time to perfect this game makes me respect them even more. Quality over rushing, always. Based on what we've seen, this will be another masterpiece."
  }
]

export function PlayerReviews() {
  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            What Players Are Saying
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Based on public demos, previews, and developer interviews, here&apos;s what the community thinks about Hollow Knight: Silksong
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reviews.map((review) => (
            <div
              key={review.id}
              className="bg-white rounded-lg p-6 shadow-sm"
            >
              <div className="mb-4">
                <h3 className="font-semibold text-gray-900">{review.name}</h3>
              </div>
              
              <p className="text-gray-700 leading-relaxed">{review.text}</p>
            </div>
          ))}
        </div>

        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 italic">
            Reviews based on public demos, previews, and developer interviews.
          </p>
        </div>
      </div>
    </section>
  )
}
