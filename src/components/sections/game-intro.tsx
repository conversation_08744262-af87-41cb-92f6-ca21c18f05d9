import Image from "next/image"

export function GameIntro() {
  return (
    <section className="py-20 md:py-32 bg-gradient-to-br from-white via-slate-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Text Content */}
          <div className="space-y-8">
            <div>
              <span className="inline-block px-4 py-2 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full text-purple-700 text-sm font-semibold mb-4">
                THE SEQUEL
              </span>
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-800 via-purple-700 to-blue-700 bg-clip-text text-transparent">
                What is Hollow Knight: Silksong?
              </h2>
            </div>
            <div className="space-y-6 text-slate-600 text-lg leading-relaxed">
              <p className="text-xl">
                Hollow Knight: Silksong is the highly anticipated sequel developed by Australian indie studio
                <span className="font-semibold text-purple-700"> Team Cherry</span>, following the critically acclaimed 2017 masterpiece Hollow Knight.
              </p>
              <p>
                This new adventure features <span className="font-semibold text-purple-700">Hornet</span> as the protagonist,
                who was a beloved character in the original game. Journey through the mysterious kingdom of
                <span className="font-semibold text-purple-700"> Pharloom</span>, a realm filled with
                forgotten lands, ancient secrets, and both insects and heroes.
              </p>
            </div>

            {/* Key Info */}
            <div className="grid grid-cols-2 gap-8 pt-8">
              <div className="bg-gradient-to-br from-purple-50 to-blue-50 p-6 rounded-2xl border border-purple-100">
                <div className="text-sm text-purple-600 font-semibold mb-2">Expected Release</div>
                <div className="text-3xl font-bold text-slate-800">2025</div>
              </div>
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-2xl border border-blue-100">
                <div className="text-sm text-blue-600 font-semibold mb-2">Genre</div>
                <div className="text-3xl font-bold text-slate-800">Metroidvania</div>
              </div>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="aspect-video rounded-lg overflow-hidden">
              <Image
                src="/images/silksong-intro.webp"
                alt="Silksong gameplay"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
