import Image from "next/image"

const features = [
  {
    title: "Deadly Acrobatic Action",
    description: "<PERSON><PERSON> has a new movement system including jumping, gliding, and a range of weapon skills, providing a fluid combat experience.",
    image: "/images/feature-combat.webp"
  },
  {
    title: "Over 150 New Enemies",
    description: "Battle with various newly designed enemies and bosses, each with unique attack patterns and abilities that will test your skills.",
    image: "/images/feature-enemies.webp"
  },
  {
    title: "Beautiful Orchestral Soundtrack",
    description: "Created by award-winning composer <PERSON> from Hollow Knight, featuring melancholic melodies and thrilling boss themes.",
    image: "/images/feature-music.webp"
  }
]

export function GameFeatures() {
  return (
    <section className="py-20 md:py-32 bg-gradient-to-br from-purple-50 via-blue-50 to-slate-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full mb-6">
            <span className="text-purple-700 font-semibold text-sm">GAME FEATURES</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-800 via-purple-700 to-blue-700 bg-clip-text text-transparent mb-6">
            What Makes Silksong Special
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Discover the incredible features that make Hollow Knight: Silksong an unforgettable adventure
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-slate-200 hover:border-purple-300 transform hover:-translate-y-2">
              <div className="relative aspect-video mb-6 rounded-xl overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>

              <h3 className="text-2xl font-bold text-slate-800 mb-4 group-hover:text-purple-700 transition-colors duration-200">
                {feature.title}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
