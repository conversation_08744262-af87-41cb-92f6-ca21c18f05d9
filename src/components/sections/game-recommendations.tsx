"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, ExternalLink } from "lucide-react"

const featuredGames = [
  {
    title: "Hollow Knight: Voidheart Edition",
    description: "The original masterpiece that started it all. Explore the haunting world of Hallownest with all DLC included.",
    image: "/images/hollow-knight.svg",
    rating: "96%",
    platform: "Steam",
    price: "$14.99",
    tags: ["Dark Fantasy", "Challenging", "Atmospheric"],
    steamUrl: "https://store.steampowered.com/app/367520/Hollow_Knight/"
  },
  {
    title: "<PERSON><PERSON> and the Will of the Wisps",
    description: "A beautiful Metroidvania with stunning hand-painted artwork and emotional storytelling.",
    image: "/images/ori.svg",
    rating: "93%",
    platform: "Steam",
    price: "$29.99",
    tags: ["Beautiful", "Emotional", "Platformer"],
    steamUrl: "https://store.steampowered.com/app/1057090/Ori_and_the_Will_of_the_Wisps/"
  },
  {
    title: "Blasphemous 2",
    description: "A dark and brutal Metroidvania with religious themes and pixel art excellence.",
    image: "/images/blasphemous.svg",
    rating: "89%",
    platform: "Steam",
    price: "$24.99",
    tags: ["Dark", "Pixel Art", "Religious"],
    steamUrl: "https://store.steampowered.com/app/2114740/Blasphemous_2/"
  },
  {
    title: "Nine Sols",
    description: "A hand-drawn Metroidvania featuring Taoist mythology and deflection-based combat.",
    image: "/images/nine-sols.svg",
    rating: "95%",
    platform: "Steam",
    price: "$24.99",
    tags: ["Hand-drawn", "Mythology", "Combat"],
    steamUrl: "https://store.steampowered.com/app/1808860/Nine_Sols/"
  },
  {
    title: "Ender Lilies: Quietus of the Knights",
    description: "A beautiful and melancholic Metroidvania with a unique purification mechanic.",
    image: "/images/ender-lilies.svg",
    rating: "91%",
    platform: "Steam",
    price: "$24.99",
    tags: ["Beautiful", "Melancholic", "Unique"],
    steamUrl: "https://store.steampowered.com/app/1369630/ENDER_LILIES_Quietus_of_the_Knights/"
  },
  {
    title: "Axiom Verge",
    description: "A sci-fi Metroidvania inspired by classics like Metroid, featuring glitch mechanics.",
    image: "/images/axiom-verge.svg",
    rating: "84%",
    platform: "Steam",
    price: "$19.99",
    tags: ["Sci-Fi", "Retro", "Glitch"],
    steamUrl: "https://store.steampowered.com/app/332200/Axiom_Verge/"
  }
]

const moreGames = [
  { title: "Afterimage", rating: "87%", tags: ["Hand-painted", "Story-rich"] },
  { title: "Bloodstained: Ritual of the Night", rating: "86%", tags: ["Gothic", "RPG Elements"] },
  { title: "Steamworld Dig 2", rating: "88%", tags: ["Mining", "Robots"] },
  { title: "Guacamelee! 2", rating: "85%", tags: ["Mexican", "Humor"] },
  { title: "The Messenger", rating: "86%", tags: ["Time Travel", "Ninja"] },
  { title: "Iconoclasts", rating: "83%", tags: ["Story-driven", "Puzzle"] },
  { title: "Supraland", rating: "89%", tags: ["3D", "Puzzle"] },
  { title: "Steamworld Dig", rating: "84%", tags: ["Mining", "Western"] },
  { title: "Yoku's Island Express", rating: "83%", tags: ["Pinball", "Unique"] },
  { title: "Timespinner", rating: "81%", tags: ["Time Travel", "Pixel Art"] },
  { title: "Ghost 1.0", rating: "82%", tags: ["Sci-Fi", "Hacking"] },
  { title: "Momodora: Reverie Under the Moonlight", rating: "85%", tags: ["Pixel Art", "Dark"] }
]

export function GameRecommendations() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [showMore, setShowMore] = useState(false)

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % featuredGames.length)
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + featuredGames.length) % featuredGames.length)
  }

  useEffect(() => {
    const interval = setInterval(nextSlide, 6000)
    return () => clearInterval(interval)
  }, [])

  return (
    <section className="py-16 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            More Metroidvania Games to Explore
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            While you wait for Silksong, discover these incredible Metroidvania games that will keep you entertained
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <div className="relative mb-12">
            <Card className="overflow-hidden shadow-lg">
              <CardContent className="p-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                  {/* Image */}
                  <div className="relative aspect-video lg:aspect-square bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 flex items-center justify-center">
                    <div className="text-white text-center p-8">
                      <div className="text-7xl mb-6">🎮</div>
                      <div className="text-2xl font-bold mb-2">{featuredGames[currentIndex].title}</div>
                      <div className="text-lg opacity-90">{featuredGames[currentIndex].price}</div>
                    </div>
                    <div className="absolute top-4 right-4">
                      <span className="bg-white/20 backdrop-blur-sm text-white text-sm font-medium px-3 py-1 rounded-full">
                        Featured
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-8 flex flex-col justify-center">
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <span className="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                          {featuredGames[currentIndex].rating}
                        </span>
                        <span className="text-gray-500 text-sm">{featuredGames[currentIndex].platform}</span>
                        <span className="text-blue-600 font-semibold text-sm">{featuredGames[currentIndex].price}</span>
                      </div>

                      <h3 className="text-2xl font-bold text-gray-900 mb-3">
                        {featuredGames[currentIndex].title}
                      </h3>

                      <p className="text-gray-600 leading-relaxed mb-4">
                        {featuredGames[currentIndex].description}
                      </p>

                      <div className="flex flex-wrap gap-2 mb-6">
                        {featuredGames[currentIndex].tags.map((tag, index) => (
                          <span
                            key={index}
                            className="bg-gray-100 text-gray-700 text-xs font-medium px-2.5 py-1 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    <Button asChild className="w-fit">
                      <a
                        href={featuredGames[currentIndex].steamUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View on Steam
                      </a>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="flex items-center justify-between mt-8">
              <Button
                variant="outline"
                size="icon"
                onClick={prevSlide}
                className="rounded-full shadow-sm"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <div className="flex space-x-2">
                {featuredGames.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="outline"
                size="icon"
                onClick={nextSlide}
                className="rounded-full shadow-sm"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* More Games Section */}
          <div className="mt-16">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Discover More Metroidvania Games
              </h3>
              <p className="text-gray-600">
                Explore our curated collection of the best Metroidvania games
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
              {moreGames.slice(0, showMore ? moreGames.length : 8).map((game, index) => (
                <div
                  key={index}
                  className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors cursor-pointer"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🎮</div>
                    <h4 className="font-semibold text-sm text-gray-900 mb-1 line-clamp-2">
                      {game.title}
                    </h4>
                    <div className="flex items-center justify-center gap-1 mb-2">
                      <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded">
                        {game.rating}
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {game.tags.slice(0, 2).map((tag, tagIndex) => (
                        <span
                          key={tagIndex}
                          className="bg-gray-200 text-gray-600 text-xs px-2 py-0.5 rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center">
              <Button
                variant="outline"
                onClick={() => setShowMore(!showMore)}
                className="px-8"
              >
                {showMore ? "Show Less" : `Show ${moreGames.length - 8} More Games`}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
