import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Monitor, Cpu, HardDrive, Zap } from "lucide-react"

const minimumReqs = [
  { icon: Monitor, label: "Operating System", value: "Windows 7" },
  { icon: Cpu, label: "Processor", value: "Intel Core 2 Duo E5200" },
  { icon: Zap, label: "Memory", value: "4 GB RAM" },
  { icon: Monitor, label: "Graphics", value: "GeForce 9800GTX+ (1GB)" },
  { icon: HardDrive, label: "Storage", value: "9 GB available space" }
]

const recommendedReqs = [
  { icon: Monitor, label: "Operating System", value: "Windows 10" },
  { icon: Cpu, label: "Processor", value: "Intel Core i5" },
  { icon: Zap, label: "Memory", value: "8 GB RAM" },
  { icon: Monitor, label: "Graphics", value: "GeForce GTX 560+" },
  { icon: HardDrive, label: "Storage", value: "9 GB available space" }
]

export function SystemRequirements() {
  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            System Requirements
          </h2>
          <p className="text-lg text-gray-600">
            Make sure your system is ready for the adventure
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Minimum Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-center">
                Minimum Requirements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {minimumReqs.map((req, index) => (
                <div key={index} className="flex items-center gap-4 p-3 rounded-lg bg-gray-50">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <req.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{req.label}</div>
                    <div className="text-sm text-gray-600">{req.value}</div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Recommended Requirements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-xl text-center">
                Recommended Requirements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {recommendedReqs.map((req, index) => (
                <div key={index} className="flex items-center gap-4 p-3 rounded-lg bg-gray-50">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <req.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{req.label}</div>
                    <div className="text-sm text-gray-600">{req.value}</div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 bg-gray-100 p-4 rounded-lg max-w-2xl mx-auto">
            <strong>Note:</strong> As of January 1, 2024, the Steam client is only supported on Windows 10 and above.
          </p>
        </div>
      </div>
    </section>
  )
}
