"use client"

import { useState } from "react"
import { ChevronDown, ChevronUp } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"

const faqs = [
  {
    question: "When will Hollow Knight: Silksong be released?",
    answer: "<PERSON> Cherry has confirmed that Hollow Knight: Silksong will be released in 2025. However, no specific date has been announced yet."
  },
  {
    question: "What platforms will Silksong be available on?",
    answer: "Hollow Knight: Silksong will be available on PC (Steam), Nintendo Switch, PlayStation, and Xbox. It will also be available on Xbox Game Pass from day one."
  },
  {
    question: "Will I need to play the original Hollow Knight first?",
    answer: "While playing the original Hollow Knight will enhance your understanding of the world and characters, Silksong is designed to be accessible to new players as well."
  },
  {
    question: "Who is the main character in Silksong?",
    answer: "The main character is <PERSON><PERSON>, who was an important NPC in the original Hollow Knight. She has her own unique moveset and abilities."
  },
  {
    question: "How long will the game be?",
    answer: "<PERSON> Cherry has stated that Silksong will be a full-length game, similar in scope to the original Hollow Knight, which typically takes 25-60 hours to complete depending on how much content you explore."
  },
  {
    question: "Will there be DLC for Silksong?",
    answer: "Team Cherry hasn't announced any DLC plans for Silksong yet. They are focused on completing the base game first."
  }
]

export function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  return (
    <section className="py-16 md:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about Hollow Knight: Silksong
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <Card key={index} className="overflow-hidden">
                <button
                  onClick={() => toggleItem(index)}
                  className="w-full text-left p-6 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 pr-4">
                      {faq.question}
                    </h3>
                    {openItems.includes(index) ? (
                      <ChevronUp className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-500 flex-shrink-0" />
                    )}
                  </div>
                </button>
                
                {openItems.includes(index) && (
                  <CardContent className="px-6 pb-6 pt-0">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
