import Link from "next/link"

export function CTA() {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-slate-900 via-purple-900 to-blue-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-full border border-purple-400/30 backdrop-blur-sm mb-8">
            <span className="text-purple-200 font-semibold text-sm">JOIN THE ADVENTURE</span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8 leading-tight">
            Ready to Enter the Kingdom of{" "}
            <span className="bg-gradient-to-r from-purple-300 to-blue-300 bg-clip-text text-transparent">
              Pharloom
            </span>
            ?
          </h2>

          <p className="text-xl text-slate-200 mb-12 max-w-3xl mx-auto leading-relaxed">
            Add Hollow Knight: Silksong to your Steam wishlist and be among the first to experience Hornet&apos;s epic journey when it launches.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href="https://store.steampowered.com/app/1030300/Hollow_Knight_Silksong/"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white hover:bg-purple-50 text-purple-600 px-8 py-4 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Add to Steam Wishlist
            </Link>
            <Link
              href="https://hollowknightsilksong.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="border-2 border-white/50 hover:border-white text-white hover:bg-white/10 px-8 py-4 rounded-xl font-semibold transition-all duration-300 backdrop-blur-sm"
            >
              Visit Official Website
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
