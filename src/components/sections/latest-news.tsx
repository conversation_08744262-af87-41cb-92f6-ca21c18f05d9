import Link from "next/link"

export function LatestNews() {
  return (
    <section className="py-16 md:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
            Latest Release Information
          </h2>

          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Official confirmation: The game will be released in 2025
            </h3>

            <div className="prose prose-lg text-gray-700 mb-6">
              <p>
                During the Nintendo Direct on April 2, 2025, <PERSON> Cherry officially confirmed that
                Hollow Knight: Silksong will be released in 2025. The Direct showed a 3-second clip
                of the game featuring <PERSON><PERSON> jumping across grassy platforms, sliding down dandelion
                slopes, and attacking a boss with her needle, generating enormous excitement among fans.
              </p>
            </div>

            <div className="text-sm text-gray-500 mb-6">
              Source: PC Gamer, April 2, 2025
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="https://store.steampowered.com/app/1030300/Hollow_Knight_Silksong/"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors text-center"
              >
                Add to Steam Wishlist
              </Link>
              <Link
                href="https://hollowknightsilksong.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="border border-gray-300 hover:border-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors text-center"
              >
                Visit Official Website
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
